import { randomUUID } from 'node:crypto';
import { NotFoundException } from '@nestjs/common';
import { ServiceRequest } from '@skillspace/grpc';

import { CreatePageDto } from '../../src/modules/editor/application/services/page.dto';
import { app, pageService } from './test-setup';

describe('PageService Integration Tests', () => {
    beforeAll(async () => {
        expect(app).toBeDefined();
        expect(pageService).toBeDefined();
    });

    describe('create', () => {
        it('должен создать страницу с минимальными данными', async () => {
            const createPageDto: CreatePageDto = {
                serviceId: 'test-service',
                entityId: 'test-entity',
                schoolId: 'test-school',
            };

            const page = await pageService.create(createPageDto);

            expect(page).toBeDefined();
            expect(page.id).toBeDefined();
            expect(page.serviceId).toBe('test-service');
            expect(page.entityId).toBe('test-entity');
            expect(page.schoolId).toBe('test-school');
            expect(page.ydoc).toBeNull();
            expect(page.createdAt).toBeDefined();
            expect(page.updatedAt).toBeDefined();
        });

        it('должен создать страницу с предустановленным pageId', async () => {
            const customPageId = randomUUID();
            const createPageDto: CreatePageDto = {
                serviceId: 'custom-service',
                entityId: 'custom-entity',
                schoolId: 'custom-school',
            };

            const page = await pageService.create(createPageDto, customPageId);

            expect(page).toBeDefined();
            expect(page.id).toBe(customPageId);
            expect(page.serviceId).toBe('custom-service');
            expect(page.entityId).toBe('custom-entity');
            expect(page.schoolId).toBe('custom-school');
            expect(page.ydoc).toBeNull();
            expect(page.createdAt).toBeDefined();
            expect(page.updatedAt).toBeDefined();
        });

        it('должен обработать ошибку при создании страницы с дублирующимся ID', async () => {
            const duplicatePageId = randomUUID();
            const createPageDto: CreatePageDto = {
                serviceId: 'duplicate-service',
                entityId: 'duplicate-entity',
                schoolId: 'duplicate-school',
            };

            // Создаем первую страницу
            await pageService.create(createPageDto, duplicatePageId);

            // Пытаемся создать вторую страницу с тем же ID
            await expect(pageService.create(createPageDto, duplicatePageId)).rejects.toThrow();
        });
    });

    describe('createManyPages', () => {
        it('должен создать несколько страниц одновременно', async () => {
            const request: ServiceRequest<'EditorService', 'CreateManyPages', 'v1'> = {
                pages: [
                    {
                        serviceId: 'bulk-service-1',
                        entityId: 'bulk-entity-1',
                        schoolId: 'bulk-school-1',
                    },
                    {
                        serviceId: 'bulk-service-2',
                        entityId: 'bulk-entity-2',
                        schoolId: 'bulk-school-2',
                    },
                    {
                        serviceId: 'bulk-service-3',
                        entityId: 'bulk-entity-3',
                        schoolId: 'bulk-school-3',
                    },
                ],
            };

            const response = await pageService.createManyPages(request);

            expect(response).toBeDefined();
            expect(response.pages).toHaveLength(3);

            // Проверяем первую страницу
            expect(response.pages[0]).toBeDefined();
            expect(response.pages[0].pageId).toBeDefined();
            expect(response.pages[0].serviceId).toBe('bulk-service-1');
            expect(response.pages[0].entityId).toBe('bulk-entity-1');
            expect(response.pages[0].schoolId).toBe('bulk-school-1');

            // Проверяем вторую страницу
            expect(response.pages[1]).toBeDefined();
            expect(response.pages[1].pageId).toBeDefined();
            expect(response.pages[1].serviceId).toBe('bulk-service-2');
            expect(response.pages[1].entityId).toBe('bulk-entity-2');
            expect(response.pages[1].schoolId).toBe('bulk-school-2');

            // Проверяем третью страницу
            expect(response.pages[2]).toBeDefined();
            expect(response.pages[2].pageId).toBeDefined();
            expect(response.pages[2].serviceId).toBe('bulk-service-3');
            expect(response.pages[2].entityId).toBe('bulk-entity-3');
            expect(response.pages[2].schoolId).toBe('bulk-school-3');

            // Проверяем, что страницы действительно созданы в БД
            for (const pageResponse of response.pages) {
                const foundPage = await pageService.findById(pageResponse.pageId);
                expect(foundPage).toBeDefined();
                expect(foundPage.id).toBe(pageResponse.pageId);
                expect(foundPage.serviceId).toBe(pageResponse.serviceId);
                expect(foundPage.entityId).toBe(pageResponse.entityId);
                expect(foundPage.schoolId).toBe(pageResponse.schoolId);
            }
        });

        it('должен создать пустой ответ при передаче пустого массива', async () => {
            const request: ServiceRequest<'EditorService', 'CreateManyPages', 'v1'> = {
                pages: [],
            };

            const response = await pageService.createManyPages(request);

            expect(response).toBeDefined();
            expect(response.pages).toHaveLength(0);
        });

        it('должен создать одну страницу', async () => {
            const request: ServiceRequest<'EditorService', 'CreateManyPages', 'v1'> = {
                pages: [
                    {
                        serviceId: 'single-service',
                        entityId: 'single-entity',
                        schoolId: 'single-school',
                    },
                ],
            };

            const response = await pageService.createManyPages(request);

            expect(response).toBeDefined();
            expect(response.pages).toHaveLength(1);
            expect(response.pages[0].serviceId).toBe('single-service');
            expect(response.pages[0].entityId).toBe('single-entity');
            expect(response.pages[0].schoolId).toBe('single-school');
        });
    });

    describe('removeManyPages', () => {
        it('должен удалить несколько существующих страниц', async () => {
            // Создаем тестовые страницы
            const createRequest: ServiceRequest<'EditorService', 'CreateManyPages', 'v1'> = {
                pages: [
                    {
                        serviceId: 'remove-service-1',
                        entityId: 'remove-entity-1',
                        schoolId: 'remove-school-1',
                    },
                    {
                        serviceId: 'remove-service-2',
                        entityId: 'remove-entity-2',
                        schoolId: 'remove-school-2',
                    },
                ],
            };

            const createResponse = await pageService.createManyPages(createRequest);
            expect(createResponse.pages).toHaveLength(2);

            // Проверяем, что страницы существуют
            for (const page of createResponse.pages) {
                const foundPage = await pageService.findById(page.pageId);
                expect(foundPage).toBeDefined();
            }

            // Удаляем страницы
            const removeRequest: ServiceRequest<'EditorService', 'RemoveManyPages', 'v1'> = {
                pageIds: createResponse.pages.map((page) => page.pageId),
            };

            const removeResponse = await pageService.removeManyPages(removeRequest);

            expect(removeResponse).toBeDefined();
            expect(removeResponse.success).toBe(true);
            expect(removeResponse.removedPageIds).toHaveLength(2);
            expect(removeResponse.removedPageIds).toContain(createResponse.pages[0].pageId);
            expect(removeResponse.removedPageIds).toContain(createResponse.pages[1].pageId);

            // Проверяем, что страницы удалены
            for (const page of createResponse.pages) {
                await expect(pageService.findById(page.pageId)).rejects.toThrow('not found');
            }
        });

        it('должен корректно обработать удаление несуществующих страниц', async () => {
            const removeRequest: ServiceRequest<'EditorService', 'RemoveManyPages', 'v1'> = {
                pageIds: [randomUUID(), randomUUID()],
            };

            const removeResponse = await pageService.removeManyPages(removeRequest);

            expect(removeResponse).toBeDefined();
            expect(removeResponse.success).toBe(true);
            expect(removeResponse.removedPageIds).toHaveLength(0); // Ни одна страница не была удалена
        });

        it('должен корректно обработать смешанный случай (существующие и несуществующие страницы)', async () => {
            // Создаем одну тестовую страницу
            const createRequest: ServiceRequest<'EditorService', 'CreateManyPages', 'v1'> = {
                pages: [
                    {
                        serviceId: 'mixed-service',
                        entityId: 'mixed-entity',
                        schoolId: 'mixed-school',
                    },
                ],
            };

            const createResponse = await pageService.createManyPages(createRequest);
            expect(createResponse.pages).toHaveLength(1);

            // Пытаемся удалить существующую и несуществующую страницы
            const removeRequest: ServiceRequest<'EditorService', 'RemoveManyPages', 'v1'> = {
                pageIds: [
                    createResponse.pages[0].pageId, // существующая
                    randomUUID(), // несуществующая
                ],
            };

            const removeResponse = await pageService.removeManyPages(removeRequest);

            expect(removeResponse).toBeDefined();
            expect(removeResponse.success).toBe(true);
            expect(removeResponse.removedPageIds).toHaveLength(1); // только существующая удалена
            expect(removeResponse.removedPageIds).toContain(createResponse.pages[0].pageId);

            // Проверяем, что существующая страница удалена
            await expect(pageService.findById(createResponse.pages[0].pageId)).rejects.toThrow('not found');
        });

        it('должен корректно обработать пустой массив', async () => {
            const removeRequest: ServiceRequest<'EditorService', 'RemoveManyPages', 'v1'> = {
                pageIds: [],
            };

            const removeResponse = await pageService.removeManyPages(removeRequest);

            expect(removeResponse).toBeDefined();
            expect(removeResponse.success).toBe(true);
            expect(removeResponse.removedPageIds).toHaveLength(0);
        });
    });

    describe('findById', () => {
        it('должен найти существующую страницу', async () => {
            // Создаем страницу
            const createdPage = await pageService.create({
                serviceId: 'find-service',
                entityId: 'find-entity',
                schoolId: 'find-school',
            });

            // Ищем её
            const foundPage = await pageService.findById(createdPage.id);

            expect(foundPage).toBeDefined();
            expect(foundPage.id).toBe(createdPage.id);
            expect(foundPage.serviceId).toBe('find-service');
            expect(foundPage.entityId).toBe('find-entity');
            expect(foundPage.schoolId).toBe('find-school');
        });

        it('должен выбросить NotFoundException для несуществующей страницы', async () => {
            const nonExistentId = randomUUID();

            await expect(pageService.findById(nonExistentId)).rejects.toThrow(NotFoundException);
            await expect(pageService.findById(nonExistentId)).rejects.toThrow(
                `Page with ID ${nonExistentId} not found`,
            );
        });
    });

    describe('findByIdOrNull', () => {
        it('должен найти существующую страницу', async () => {
            // Создаем страницу
            const createdPage = await pageService.create({
                serviceId: 'find-or-null-service',
                entityId: 'find-or-null-entity',
                schoolId: 'find-or-null-school',
            });

            // Ищем её
            const foundPage = await pageService.findByIdOrNull(createdPage.id);

            expect(foundPage).toBeDefined();
            expect(foundPage!.id).toBe(createdPage.id);
            expect(foundPage!.serviceId).toBe('find-or-null-service');
            expect(foundPage!.entityId).toBe('find-or-null-entity');
            expect(foundPage!.schoolId).toBe('find-or-null-school');
        });

        it('должен вернуть null для несуществующей страницы', async () => {
            const nonExistentId = randomUUID();

            const result = await pageService.findByIdOrNull(nonExistentId);

            expect(result).toBeNull();
        });

        it('должен корректно обработать ошибку базы данных', async () => {
            // Тестируем с некорректным ID (не UUID)
            const invalidId = 'invalid-id';

            const result = await pageService.findByIdOrNull(invalidId);

            expect(result).toBeUndefined(); // метод возвращает undefined при ошибке
        });
    });

    describe('forceDelete', () => {
        it('должен удалить существующую страницу', async () => {
            // Создаем страницу
            const createdPage = await pageService.create({
                serviceId: 'delete-service',
                entityId: 'delete-entity',
                schoolId: 'delete-school',
            });

            // Проверяем, что она существует
            const foundPage = await pageService.findById(createdPage.id);
            expect(foundPage).toBeDefined();

            // Удаляем её
            await pageService.forceDelete(createdPage.id);

            // Проверяем, что она больше не существует
            await expect(pageService.findById(createdPage.id)).rejects.toThrow(NotFoundException);
        });

        it('должен выбросить NotFoundException при попытке удалить несуществующую страницу', async () => {
            const nonExistentId = randomUUID();

            await expect(pageService.forceDelete(nonExistentId)).rejects.toThrow(NotFoundException);
            await expect(pageService.forceDelete(nonExistentId)).rejects.toThrow(
                `Page with ID ${nonExistentId} not found`,
            );
        });
    });

    describe('update', () => {
        it('должен обновить содержимое страницы', async () => {
            // Создаем страницу
            const createdPage = await pageService.create({
                serviceId: 'update-service',
                entityId: 'update-entity',
                schoolId: 'update-school',
            });

            // Обновляем её
            const updateData = {
                content: {
                    type: 'doc',
                    content: [
                        {
                            type: 'paragraph',
                            content: [
                                {
                                    type: 'text',
                                    text: 'Updated content',
                                },
                            ],
                        },
                    ],
                },
                textContent: 'Updated content',
            };

            await pageService.update(createdPage.id, updateData);

            // Проверяем, что обновление прошло успешно
            const updatedPage = await pageService.findById(createdPage.id);
            expect(updatedPage.content).toEqual(updateData.content);
            expect(updatedPage.textContent).toBe(updateData.textContent);
        });

        it('должен обновить только указанные поля', async () => {
            // Создаем страницу
            const createdPage = await pageService.create({
                serviceId: 'partial-update-service',
                entityId: 'partial-update-entity',
                schoolId: 'partial-update-school',
            });

            // Обновляем только textContent
            const updateData = {
                textContent: 'Only text content updated',
            };

            await pageService.update(createdPage.id, updateData);

            // Проверяем, что обновление прошло успешно
            const updatedPage = await pageService.findById(createdPage.id);
            expect(updatedPage.textContent).toBe(updateData.textContent);
            expect(updatedPage.content).toBeNull(); // Должно остаться null
        });

        it('должен обновить поле updatedAt при обновлении', async () => {
            // Создаем страницу
            const createdPage = await pageService.create({
                serviceId: 'timestamp-service',
                entityId: 'timestamp-entity',
                schoolId: 'timestamp-school',
            });

            const originalUpdatedAt = createdPage.updatedAt;

            // Ждем немного, чтобы время изменилось
            await new Promise((resolve) => setTimeout(resolve, 10));

            // Обновляем страницу
            const updateData = {
                textContent: 'Updated for timestamp test',
            };

            await pageService.update(createdPage.id, updateData);

            // Проверяем, что updatedAt изменился
            const updatedPage = await pageService.findById(createdPage.id);
            expect(updatedPage.updatedAt.getTime()).toBeGreaterThan(originalUpdatedAt.getTime());
        });

        it('должен корректно обработать обновление несуществующей страницы', async () => {
            const nonExistentId = randomUUID();
            const updateData = {
                textContent: 'This should not work',
            };

            // Обновление несуществующей страницы не должно выбрасывать ошибку
            // но и не должно ничего обновлять
            await expect(pageService.update(nonExistentId, updateData)).resolves.not.toThrow();
        });
    });

    describe('edge cases', () => {
        it('должен создать страницу с минимальными данными', async () => {
            const createPageDto: CreatePageDto = {
                serviceId: 'edge-service',
                entityId: 'edge-entity',
                schoolId: 'edge-school',
            };

            const page = await pageService.create(createPageDto);

            expect(page).toBeDefined();
            expect(page.serviceId).toBe('edge-service');
            expect(page.entityId).toBe('edge-entity');
            expect(page.schoolId).toBe('edge-school');
        });

        it('должен создать страницу с null content по умолчанию', async () => {
            const createPageDto: CreatePageDto = {
                serviceId: 'null-service',
                entityId: 'null-entity',
                schoolId: 'null-school',
            };

            const page = await pageService.create(createPageDto);

            expect(page).toBeDefined();
            expect(page.content).toBeNull();
            expect(page.ydoc).toBeNull();
        });

        it('должен создать страницу с уникальными entityId', async () => {
            const createPageDto1: CreatePageDto = {
                serviceId: 'unique-service',
                entityId: 'unique-entity-1',
                schoolId: 'unique-school',
            };

            const createPageDto2: CreatePageDto = {
                serviceId: 'unique-service',
                entityId: 'unique-entity-2',
                schoolId: 'unique-school',
            };

            const page1 = await pageService.create(createPageDto1);
            const page2 = await pageService.create(createPageDto2);

            expect(page1).toBeDefined();
            expect(page2).toBeDefined();
            expect(page1.id).not.toBe(page2.id);
            expect(page1.entityId).toBe('unique-entity-1');
            expect(page2.entityId).toBe('unique-entity-2');
        });

        it('должен корректно обработать специальные символы в полях', async () => {
            const createPageDto: CreatePageDto = {
                serviceId: 'service-with-special-chars-!@#$%^&*()',
                entityId: 'entity_with_underscores_and_numbers_123',
                schoolId: 'school-with-émojis-🏫-and-unicode-ñ',
            };

            const page = await pageService.create(createPageDto);

            expect(page).toBeDefined();
            expect(page.serviceId).toBe('service-with-special-chars-!@#$%^&*()');
            expect(page.entityId).toBe('entity_with_underscores_and_numbers_123');
            expect(page.schoolId).toBe('school-with-émojis-🏫-and-unicode-ñ');
        });

        it('должен корректно обработать длинные строки', async () => {
            const longServiceId = 'service-' + 'A'.repeat(200);
            const longEntityId = 'entity-' + 'B'.repeat(200);
            const longSchoolId = 'school-' + 'C'.repeat(200);

            const createPageDto: CreatePageDto = {
                serviceId: longServiceId,
                entityId: longEntityId,
                schoolId: longSchoolId,
            };

            const page = await pageService.create(createPageDto);

            expect(page).toBeDefined();
            expect(page.serviceId).toBe(longServiceId);
            expect(page.entityId).toBe(longEntityId);
            expect(page.schoolId).toBe(longSchoolId);
        });

        it('должен корректно обработать пустые строки в полях', async () => {
            const createPageDto: CreatePageDto = {
                serviceId: '',
                entityId: '',
                schoolId: '',
            };

            const page = await pageService.create(createPageDto);

            expect(page).toBeDefined();
            expect(page.serviceId).toBe('');
            expect(page.entityId).toBe('');
            expect(page.schoolId).toBe('');
        });
    });

    describe('performance and concurrency', () => {
        it('должен корректно обработать одновременное создание множества страниц', async () => {
            const promises = [];
            const pageCount = 10;

            for (let i = 0; i < pageCount; i++) {
                const createPageDto: CreatePageDto = {
                    serviceId: `concurrent-service-${i}`,
                    entityId: `concurrent-entity-${i}`,
                    schoolId: `concurrent-school-${i}`,
                };
                promises.push(pageService.create(createPageDto));
            }

            const pages = await Promise.all(promises);

            expect(pages).toHaveLength(pageCount);

            // Проверяем, что все страницы имеют уникальные ID
            const pageIds = pages.map((page) => page.id);
            const uniqueIds = new Set(pageIds);
            expect(uniqueIds.size).toBe(pageCount);

            // Проверяем, что все страницы созданы корректно
            for (let i = 0; i < pageCount; i++) {
                expect(pages[i].serviceId).toBe(`concurrent-service-${i}`);
                expect(pages[i].entityId).toBe(`concurrent-entity-${i}`);
                expect(pages[i].schoolId).toBe(`concurrent-school-${i}`);
            }
        });

        it('должен корректно обработать одновременное удаление множества страниц', async () => {
            // Создаем страницы
            const createRequest: ServiceRequest<'EditorService', 'CreateManyPages', 'v1'> = {
                pages: Array.from({ length: 5 }, (_, i) => ({
                    serviceId: `concurrent-delete-service-${i}`,
                    entityId: `concurrent-delete-entity-${i}`,
                    schoolId: `concurrent-delete-school-${i}`,
                })),
            };

            const createResponse = await pageService.createManyPages(createRequest);
            expect(createResponse.pages).toHaveLength(5);

            // Одновременно удаляем страницы
            const deletePromises = createResponse.pages.map((page) => pageService.forceDelete(page.pageId));

            await Promise.all(deletePromises);

            // Проверяем, что все страницы удалены
            for (const page of createResponse.pages) {
                await expect(pageService.findById(page.pageId)).rejects.toThrow(NotFoundException);
            }
        });
    });
});
